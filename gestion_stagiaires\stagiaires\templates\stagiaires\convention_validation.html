{% extends 'stagiaires/base.html' %}

{% block title %}Validation Convention - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Validation de Convention de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p><strong>Établissement :</strong> {{ stagiaire.etablissement }}</p>
                                    <p><strong>Niveau :</strong> {{ stagiaire.niveau_etude }}</p>
                                    <p><strong>Spécialité :</strong> {{ stagiaire.specialite }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Détails du stage</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                    <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                    <p><strong>Période :</strong> {{ stagiaire.date_debut }} - {{ stagiaire.date_fin }}</p>
                                    <p><strong>Durée :</strong> {{ stagiaire.duree_stage }} jours</p>
                                    <p><strong>Statut :</strong> 
                                        <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}primary{% elif stagiaire.statut == 'TERMINE' %}success{% else %}secondary{% endif %}">
                                            {{ stagiaire.get_statut_display }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Convention à valider -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-file-contract me-2"></i>Convention à valider</h6>
                        </div>
                        <div class="card-body">
                            {% if stagiaire.convention_stage %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1"><strong>Fichier :</strong> {{ stagiaire.convention_stage.name|default:"Convention de stage" }}</p>
                                        <p class="mb-1"><strong>Statut actuel :</strong> 
                                            <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                                {{ stagiaire.get_statut_convention_display }}
                                            </span>
                                        </p>
                                        {% if stagiaire.date_validation_convention %}
                                            <p class="mb-1"><strong>Validée le :</strong> {{ stagiaire.date_validation_convention|date:"d/m/Y à H:i" }}</p>
                                            <p class="mb-1"><strong>Validée par :</strong> {{ stagiaire.validee_par.get_full_name }}</p>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <a href="{{ stagiaire.convention_stage.url }}" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i>Voir la convention
                                        </a>
                                        <a href="{{ stagiaire.convention_stage.url }}" download class="btn btn-outline-primary ms-2">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                    </div>
                                </div>
                                
                                {% if stagiaire.commentaire_convention %}
                                <div class="mt-3">
                                    <strong>Commentaires précédents :</strong>
                                    <div class="alert alert-light">{{ stagiaire.commentaire_convention }}</div>
                                </div>
                                {% endif %}
                            {% else %}
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Aucune convention uploadée pour ce stagiaire.
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Formulaire de validation -->
                    {% if stagiaire.convention_stage %}
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.statut_convention.id_for_label }}" class="form-label">
                                    <i class="fas fa-clipboard-check me-1"></i>Décision de validation
                                </label>
                                {{ form.statut_convention }}
                                {% if form.statut_convention.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.statut_convention.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.commentaire_convention.id_for_label }}" class="form-label">
                                <i class="fas fa-comment me-1"></i>Commentaires de validation
                            </label>
                            {{ form.commentaire_convention }}
                            {% if form.commentaire_convention.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.commentaire_convention.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Ajoutez vos commentaires sur la validation (obligatoire en cas de rejet ou modification demandée).
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'conventions_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>Valider la décision
                            </button>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-header h6 {
    margin-bottom: 0;
}
.card-body p {
    margin-bottom: 8px;
}
</style>
{% endblock %}
