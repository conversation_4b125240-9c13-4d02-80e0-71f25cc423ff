from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.views import LoginView
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.template.loader import render_to_string
from .forms import (CustomUserCreationForm, CustomAuthenticationForm, StagiaireForm,
                   ConventionUploadForm, ConventionValidationForm, TacheStageForm,
                   EvaluationStageForm, AttestationGenerationForm, MissionForm,
                   SuiviAvancementForm, RapportStageForm, ValidationRapportForm)
from .models import Custom<PERSON>ser, Stagiaire, TacheStage, Mission, RapportStage
import os
from datetime import date


class CustomLoginView(LoginView):
    """Vue de connexion personnalisée"""
    form_class = CustomAuthenticationForm
    template_name = 'stagiaires/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('dashboard')

    def form_valid(self, form):
        messages.success(self.request, f'Bienvenue {form.get_user().first_name}!')
        return super().form_valid(form)


class RegisterView(CreateView):
    """Vue d'inscription personnalisée"""
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'stagiaires/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(
            self.request,
            'Votre compte a été créé avec succès! Vous pouvez maintenant vous connecter.'
        )
        return response

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('dashboard')
        return super().dispatch(request, *args, **kwargs)


def dashboard_view(request):
    """Vue du tableau de bord après connexion"""
    if not request.user.is_authenticated:
        return redirect('login')

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/dashboard.html', context)


def logout_view(request):
    """Vue de déconnexion"""
    logout(request)
    messages.info(request, 'Vous avez été déconnecté avec succès.')
    return redirect('login')


@login_required
def stagiaires_list_view(request):
    """Vue pour afficher la liste des stagiaires"""
    # Récupérer tous les stagiaires (partagés entre tous les utilisateurs)
    stagiaires = Stagiaire.objects.all().select_related('encadrant', 'cree_par')

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'stagiaires': stagiaires
    }
    return render(request, 'stagiaires/stagiaires_list.html', context)


@login_required
def add_stagiaire_view(request):
    """Vue pour ajouter un stagiaire"""
    if request.method == 'POST':
        form = StagiaireForm(request.POST)
        if form.is_valid():
            stagiaire = form.save(commit=False)
            stagiaire.cree_par = request.user  # Enregistrer qui a créé le stagiaire
            stagiaire.save()
            messages.success(request, f'Le stagiaire {stagiaire.nom_complet} a été ajouté avec succès!')
            return redirect('stagiaires_list')
    else:
        form = StagiaireForm()

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'form': form
    }
    return render(request, 'stagiaires/add_stagiaire.html', context)


@login_required
def reports_view(request):
    """Vue pour afficher les rapports et statistiques"""
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/reports.html', context)


@login_required
def export_view(request):
    """Vue pour exporter les données"""
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/export.html', context)


@login_required
def user_management_view(request):
    """Vue pour la gestion des utilisateurs (ADMIN uniquement)"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé. Cette page est réservée aux administrateurs.')
        return redirect('dashboard')

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display(),
        'users': CustomUser.objects.all()
    }
    return render(request, 'stagiaires/user_management.html', context)


@login_required
def contracts_view(request):
    """Vue pour la gestion des contrats de stage (RH et ADMIN)"""
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé. Cette page est réservée aux gestionnaires RH et administrateurs.')
        return redirect('dashboard')

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/contracts.html', context)

# ===== GESTION DES CONVENTIONS DE STAGE =====

@login_required
def convention_upload_view(request, stagiaire_id):
    """Vue pour uploader une convention de stage"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('stagiaires_list')

    if request.method == 'POST':
        form = ConventionUploadForm(request.POST, request.FILES, instance=stagiaire)
        if form.is_valid():
            form.save()
            messages.success(request, f'Convention uploadée avec succès pour {stagiaire.nom_complet}.')
            return redirect('stagiaires_list')
    else:
        form = ConventionUploadForm(instance=stagiaire)

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/convention_upload.html', context)


@login_required
def convention_validation_view(request, stagiaire_id):
    """Vue pour valider une convention de stage"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('stagiaires_list')

    # Vérifier qu'une convention existe
    if not stagiaire.convention_stage:
        messages.error(request, 'Aucune convention à valider pour ce stagiaire.')
        return redirect('stagiaires_list')

    if request.method == 'POST':
        form = ConventionValidationForm(request.POST, instance=stagiaire)
        if form.is_valid():
            stagiaire = form.save(commit=False)
            if stagiaire.statut_convention == 'VALIDEE':
                stagiaire.date_validation_convention = timezone.now()
                stagiaire.validee_par = request.user
            stagiaire.save()

            status_msg = {
                'VALIDEE': 'validée',
                'REJETEE': 'rejetée',
                'MODIFIEE': 'marquée à modifier'
            }
            messages.success(request, f'Convention {status_msg.get(stagiaire.statut_convention, "mise à jour")} pour {stagiaire.nom_complet}.')
            return redirect('stagiaires_list')
    else:
        form = ConventionValidationForm(instance=stagiaire)

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/convention_validation.html', context)


@login_required
def conventions_list_view(request):
    """Vue pour lister toutes les conventions à valider"""
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Filtrer les stagiaires avec conventions
    stagiaires_avec_conventions = Stagiaire.objects.filter(
        convention_stage__isnull=False
    ).select_related('encadrant', 'validee_par').order_by('-date_creation')

    context = {
        'stagiaires': stagiaires_avec_conventions,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/conventions_list.html', context)


# ===== GESTION DES TÂCHES DE STAGE =====

@login_required
def taches_stagiaire_view(request, stagiaire_id):
    """Vue pour gérer les tâches d'un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role'):
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Les encadrants ne peuvent voir que leurs stagiaires
    if request.user.role == 'ENCADRANT' and stagiaire.encadrant != request.user:
        messages.error(request, 'Vous ne pouvez voir que vos stagiaires.')
        return redirect('stagiaires_list')

    taches = stagiaire.taches.all().order_by('priorite', 'date_debut_prevue')

    context = {
        'stagiaire': stagiaire,
        'taches': taches,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/taches_stagiaire.html', context)


@login_required
def add_tache_view(request, stagiaire_id):
    """Vue pour ajouter une tâche à un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Les encadrants ne peuvent ajouter des tâches qu'à leurs stagiaires
    if request.user.role == 'ENCADRANT' and stagiaire.encadrant != request.user:
        messages.error(request, 'Vous ne pouvez ajouter des tâches qu\'à vos stagiaires.')
        return redirect('stagiaires_list')

    if request.method == 'POST':
        form = TacheStageForm(request.POST)
        if form.is_valid():
            tache = form.save(commit=False)
            tache.stagiaire = stagiaire
            tache.creee_par = request.user
            tache.save()
            messages.success(request, f'Tâche "{tache.titre}" ajoutée avec succès.')
            return redirect('taches_stagiaire', stagiaire_id=stagiaire.id)
    else:
        form = TacheStageForm()

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/add_tache.html', context)


# ===== GESTION DES ÉVALUATIONS =====

@login_required
def evaluation_stagiaire_view(request, stagiaire_id):
    """Vue pour évaluer un stagiaire (encadrant)"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role'):
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Seuls l'encadrant, RH et admin peuvent évaluer
    if (request.user.role == 'ENCADRANT' and stagiaire.encadrant != request.user) or \
       (request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']):
        messages.error(request, 'Vous ne pouvez évaluer que vos stagiaires.')
        return redirect('stagiaires_list')

    if request.method == 'POST':
        form = EvaluationStageForm(request.POST, instance=stagiaire)
        if form.is_valid():
            form.save()
            messages.success(request, f'Évaluation de {stagiaire.nom_complet} mise à jour avec succès.')
            return redirect('stagiaires_list')
    else:
        form = EvaluationStageForm(instance=stagiaire)

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/evaluation_stagiaire.html', context)


# ===== GESTION DES ATTESTATIONS =====

@login_required
def generer_attestation_view(request, stagiaire_id):
    """Vue pour générer une attestation de fin de stage"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé. Seuls les RH et administrateurs peuvent générer des attestations.')
        return redirect('stagiaires_list')

    # Vérifier si l'attestation peut être générée
    if not stagiaire.peut_generer_attestation:
        error_msg = []
        if not stagiaire.stage_termine:
            error_msg.append("le stage n'est pas encore terminé")
        if stagiaire.statut_taches != 'ACCOMPLIES':
            error_msg.append("toutes les tâches ne sont pas accomplies")
        if stagiaire.statut_convention != 'VALIDEE':
            error_msg.append("la convention n'est pas validée")

        messages.error(request, f'Impossible de générer l\'attestation : {", ".join(error_msg)}.')
        return redirect('stagiaires_list')

    if request.method == 'POST':
        form = AttestationGenerationForm(request.POST)
        if form.is_valid():
            # Générer l'attestation (ici on simule avec un fichier texte)
            attestation_content = generer_contenu_attestation(stagiaire, form.cleaned_data)

            # Sauvegarder les métadonnées
            stagiaire.date_generation_attestation = timezone.now()
            stagiaire.attestation_generee_par = request.user
            stagiaire.save()

            # Retourner le fichier
            response = HttpResponse(attestation_content, content_type='text/plain')
            response['Content-Disposition'] = f'attachment; filename="attestation_{stagiaire.nom}_{stagiaire.prenom}.txt"'

            messages.success(request, f'Attestation générée avec succès pour {stagiaire.nom_complet}.')
            return response
    else:
        form = AttestationGenerationForm()

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/generer_attestation.html', context)


def generer_contenu_attestation(stagiaire, form_data):
    """Génère le contenu de l'attestation"""
    template_choice = form_data.get('template_choice', 'standard')
    commentaires = form_data.get('commentaires_supplementaires', '')

    content = f"""
ATTESTATION DE FIN DE STAGE

Je soussigné(e), {stagiaire.attestation_generee_par.get_full_name()},
en qualité de {stagiaire.attestation_generee_par.get_role_display()},

ATTESTE PAR LA PRÉSENTE QUE :

Monsieur/Madame {stagiaire.nom_complet}
Né(e) le {stagiaire.date_naissance.strftime('%d/%m/%Y')}
Étudiant(e) en {stagiaire.niveau_etude} - {stagiaire.specialite}
À {stagiaire.etablissement}

A effectué un stage au sein de notre organisation du {stagiaire.date_debut.strftime('%d/%m/%Y')} au {stagiaire.date_fin.strftime('%d/%m/%Y')},
soit une durée de {stagiaire.duree_stage} jours.

Département d'affectation : {stagiaire.get_departement_display()}
Encadrant : {stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Non spécifié'}

ÉVALUATION :
{stagiaire.evaluation_encadrant if stagiaire.evaluation_encadrant else 'Aucune évaluation fournie'}

Note finale : {stagiaire.note_finale if stagiaire.note_finale else 'Non notée'}/20

Statut des tâches : {stagiaire.get_statut_taches_display()}

{f'COMMENTAIRES SUPPLÉMENTAIRES :\\n{commentaires}' if commentaires else ''}

Cette attestation est délivrée pour servir et valoir ce que de droit.

Fait le {timezone.now().strftime('%d/%m/%Y')}

Signature :
{stagiaire.attestation_generee_par.get_full_name()}
{stagiaire.attestation_generee_par.get_role_display()}
"""

    return content


@login_required
def attestations_list_view(request):
    """Vue pour lister toutes les attestations générées"""
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Stagiaires avec attestations générées
    stagiaires_avec_attestations = Stagiaire.objects.filter(
        date_generation_attestation__isnull=False
    ).select_related('encadrant', 'attestation_generee_par').order_by('-date_generation_attestation')

    # Stagiaires éligibles pour attestation
    stagiaires_eligibles = Stagiaire.objects.filter(
        date_generation_attestation__isnull=True,
        statut_taches='ACCOMPLIES',
        statut_convention='VALIDEE',
        date_fin__lt=date.today()
    ).select_related('encadrant')

    context = {
        'stagiaires_avec_attestations': stagiaires_avec_attestations,
        'stagiaires_eligibles': stagiaires_eligibles,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/attestations_list.html', context)


# ==================== VUES POUR LES MISSIONS ====================

@login_required
def missions_stagiaire_view(request, stagiaire_id):
    """Vue pour afficher les missions d'un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    missions = Mission.objects.filter(stagiaire=stagiaire).order_by('-date_creation')

    # Statistiques
    total_missions = missions.count()
    missions_terminees = missions.filter(statut='TERMINEE').count()
    missions_en_cours = missions.filter(statut='EN_COURS').count()
    missions_en_retard = missions.filter(statut__in=['PLANIFIEE', 'EN_COURS']).filter(date_fin_prevue__lt=timezone.now().date()).count()

    context = {
        'stagiaire': stagiaire,
        'missions': missions,
        'total_missions': total_missions,
        'missions_terminees': missions_terminees,
        'missions_en_cours': missions_en_cours,
        'missions_en_retard': missions_en_retard,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/missions_stagiaire.html', context)


@login_required
def planifier_mission_view(request, stagiaire_id):
    """Vue pour planifier une nouvelle mission pour un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions (seuls les encadrants peuvent planifier)
    if request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']:
        messages.error(request, 'Seuls les encadrants peuvent planifier des missions.')
        return redirect('missions_stagiaire', stagiaire_id=stagiaire_id)

    if request.method == 'POST':
        form = MissionForm(request.POST, stagiaire=stagiaire)
        if form.is_valid():
            mission = form.save(commit=False)
            mission.stagiaire = stagiaire
            mission.creee_par = request.user
            mission.save()
            messages.success(request, f'Mission "{mission.titre}" planifiée avec succès.')
            return redirect('missions_stagiaire', stagiaire_id=stagiaire_id)
    else:
        form = MissionForm(stagiaire=stagiaire)

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/planifier_mission.html', context)


@login_required
def suivi_mission_view(request, mission_id):
    """Vue pour le suivi d'avancement d'une mission"""
    mission = get_object_or_404(Mission, id=mission_id)

    # Vérifier les permissions
    if request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = SuiviAvancementForm(request.POST, instance=mission)
        if form.is_valid():
            mission_updated = form.save(commit=False)

            # Mettre à jour les dates réelles selon le statut
            if mission_updated.statut == 'EN_COURS' and not mission.date_debut_reelle:
                mission_updated.date_debut_reelle = timezone.now().date()
            elif mission_updated.statut == 'TERMINEE' and not mission.date_fin_reelle:
                mission_updated.date_fin_reelle = timezone.now().date()
                mission_updated.pourcentage_avancement = 100

            mission_updated.save()
            messages.success(request, 'Avancement de la mission mis à jour avec succès.')
            return redirect('missions_stagiaire', stagiaire_id=mission.stagiaire.id)
    else:
        form = SuiviAvancementForm(instance=mission)

    context = {
        'form': form,
        'mission': mission,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/suivi_mission.html', context)


# ==================== VUES POUR LES RAPPORTS ====================

@login_required
def rapports_stagiaire_view(request, stagiaire_id):
    """Vue pour afficher les rapports d'un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    # Vérifier les permissions
    if request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    rapports = RapportStage.objects.filter(stagiaire=stagiaire).order_by('-date_creation')

    # Statistiques
    total_rapports = rapports.count()
    rapports_valides = rapports.filter(statut='VALIDE').count()
    rapports_en_attente = rapports.filter(statut='SOUMIS').count()
    rapports_rejetes = rapports.filter(statut='REJETE').count()

    context = {
        'stagiaire': stagiaire,
        'rapports': rapports,
        'total_rapports': total_rapports,
        'rapports_valides': rapports_valides,
        'rapports_en_attente': rapports_en_attente,
        'rapports_rejetes': rapports_rejetes,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/rapports_stagiaire.html', context)


@login_required
def soumettre_rapport_view(request, stagiaire_id):
    """Vue pour soumettre un nouveau rapport"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)

    if request.method == 'POST':
        form = RapportStageForm(request.POST, request.FILES, stagiaire=stagiaire)
        if form.is_valid():
            rapport = form.save(commit=False)
            rapport.stagiaire = stagiaire
            rapport.statut = 'SOUMIS'
            rapport.save()
            messages.success(request, f'Rapport "{rapport.titre}" soumis avec succès.')
            return redirect('rapports_stagiaire', stagiaire_id=stagiaire_id)
    else:
        form = RapportStageForm(stagiaire=stagiaire)

    context = {
        'form': form,
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/soumettre_rapport.html', context)


@login_required
def valider_rapport_view(request, rapport_id):
    """Vue pour valider un rapport par l'encadrant"""
    rapport = get_object_or_404(RapportStage, id=rapport_id)

    # Vérifier les permissions (seuls les encadrants peuvent valider)
    if request.user.role not in ['ENCADRANT', 'RH', 'ADMIN']:
        messages.error(request, 'Seuls les encadrants peuvent valider les rapports.')
        return redirect('rapports_stagiaire', stagiaire_id=rapport.stagiaire.id)

    if request.method == 'POST':
        form = ValidationRapportForm(request.POST, instance=rapport)
        if form.is_valid():
            rapport_updated = form.save(commit=False)
            rapport_updated.valide_par = request.user

            if rapport_updated.statut == 'VALIDE':
                rapport_updated.date_validation = timezone.now()

            rapport_updated.save()
            messages.success(request, f'Rapport "{rapport.titre}" validé avec succès.')
            return redirect('rapports_stagiaire', stagiaire_id=rapport.stagiaire.id)
    else:
        form = ValidationRapportForm(instance=rapport)

    context = {
        'form': form,
        'rapport': rapport,
        'user': request.user,
        'role_display': request.user.get_role_display()
    }
    return render(request, 'stagiaires/valider_rapport.html', context)


# Create your views here.
