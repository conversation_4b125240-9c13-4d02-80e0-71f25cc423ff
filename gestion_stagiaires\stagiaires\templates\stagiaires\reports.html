{% extends 'stagiaires/base.html' %}

{% block title %}Rapports et Statistiques - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Rapports et Statistiques
                    </h3>
                    <a href="{% url 'dashboard' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                    </a>
                </div>
                <div class="card-body">
                    <!-- Statistiques générales -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4>15</h4>
                                    <p class="mb-0">Stagiaires actifs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h4>8</h4>
                                    <p class="mb-0">Stages terminés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4>3</h4>
                                    <p class="mb-0">En attente</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar fa-2x mb-2"></i>
                                    <h4>26</h4>
                                    <p class="mb-0">Total cette année</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Graphiques -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-pie-chart me-1"></i>Répartition par département</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-line-chart me-1"></i>Évolution mensuelle</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tableaux de données -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-building me-1"></i>Statistiques par département</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Département</th>
                                                <th>Actifs</th>
                                                <th>Terminés</th>
                                                <th>Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Informatique</td>
                                                <td><span class="badge bg-primary">8</span></td>
                                                <td><span class="badge bg-success">5</span></td>
                                                <td><strong>13</strong></td>
                                            </tr>
                                            <tr>
                                                <td>Marketing</td>
                                                <td><span class="badge bg-primary">4</span></td>
                                                <td><span class="badge bg-success">2</span></td>
                                                <td><strong>6</strong></td>
                                            </tr>
                                            <tr>
                                                <td>RH</td>
                                                <td><span class="badge bg-primary">2</span></td>
                                                <td><span class="badge bg-success">1</span></td>
                                                <td><strong>3</strong></td>
                                            </tr>
                                            <tr>
                                                <td>Finance</td>
                                                <td><span class="badge bg-primary">1</span></td>
                                                <td><span class="badge bg-success">0</span></td>
                                                <td><strong>1</strong></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-graduation-cap me-1"></i>Niveaux d'études</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Niveau</th>
                                                <th>Nombre</th>
                                                <th>Pourcentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>BAC+5</td>
                                                <td><span class="badge bg-primary">10</span></td>
                                                <td>38%</td>
                                            </tr>
                                            <tr>
                                                <td>BAC+3</td>
                                                <td><span class="badge bg-primary">8</span></td>
                                                <td>31%</td>
                                            </tr>
                                            <tr>
                                                <td>BAC+4</td>
                                                <td><span class="badge bg-primary">5</span></td>
                                                <td>19%</td>
                                            </tr>
                                            <tr>
                                                <td>BAC+2</td>
                                                <td><span class="badge bg-primary">3</span></td>
                                                <td>12%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5><i class="fas fa-download me-1"></i>Actions rapides</h5>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'export' %}" class="btn btn-outline-primary">
                                            <i class="fas fa-file-excel me-1"></i>Exporter Excel
                                        </a>
                                        <button class="btn btn-outline-secondary">
                                            <i class="fas fa-file-pdf me-1"></i>Générer PDF
                                        </button>
                                        <button class="btn btn-outline-info">
                                            <i class="fas fa-print me-1"></i>Imprimer
                                        </button>
                                        <button class="btn btn-outline-success">
                                            <i class="fas fa-sync me-1"></i>Actualiser
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Graphique en secteurs pour les départements
    const deptCtx = document.getElementById('departmentChart').getContext('2d');
    new Chart(deptCtx, {
        type: 'pie',
        data: {
            labels: ['Informatique', 'Marketing', 'RH', 'Finance'],
            datasets: [{
                data: [13, 6, 3, 1],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#17a2b8']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Graphique linéaire pour l'évolution mensuelle
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            datasets: [{
                label: 'Nouveaux stagiaires',
                data: [3, 5, 2, 8, 4, 6],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}
