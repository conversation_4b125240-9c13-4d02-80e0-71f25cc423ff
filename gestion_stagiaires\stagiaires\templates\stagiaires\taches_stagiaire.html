{% extends 'stagiaires/base.html' %}

{% block title %}Tâches - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>
                                Gestion des Tâches de Stage
                            </h4>
                            <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                        </div>
                        <div>
                            {% if user.role in 'RH,ADMIN,ENCADRANT' %}
                            <a href="{% url 'add_tache' stagiaire.id %}" class="btn btn-light">
                                <i class="fas fa-plus me-1"></i>Ajouter une tâche
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6><i class="fas fa-user me-2"></i>Informations du stage</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Stagiaire :</strong> {{ stagiaire.nom_complet }}</p>
                                            <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                            <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                            <p><strong>Durée :</strong> {{ stagiaire.duree_stage }} jours</p>
                                            <p><strong>Statut :</strong> 
                                                <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}primary{% elif stagiaire.statut == 'TERMINE' %}success{% else %}secondary{% endif %}">
                                                    {{ stagiaire.get_statut_display }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h6><i class="fas fa-chart-pie me-2"></i>Progression</h6>
                                    {% with total_taches=taches|length taches_terminees=taches|length %}
                                    {% if total_taches > 0 %}
                                        {% for tache in taches %}
                                            {% if tache.statut == 'TERMINEE' %}
                                                {% with taches_terminees=forloop.counter %}{% endwith %}
                                            {% endif %}
                                        {% endfor %}
                                        <div class="progress mb-2">
                                            <div class="progress-bar bg-success" style="width: {% widthratio taches_terminees total_taches 100 %}%"></div>
                                        </div>
                                        <small>{{ taches_terminees }}/{{ total_taches }} tâches terminées</small>
                                    {% else %}
                                        <p class="text-muted">Aucune tâche assignée</p>
                                    {% endif %}
                                    {% endwith %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Liste des tâches -->
                    {% if taches %}
                    <div class="row">
                        {% for tache in taches %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-{% if tache.statut == 'TERMINEE' %}success{% elif tache.statut == 'EN_COURS' %}primary{% elif tache.statut == 'EN_RETARD' %}danger{% else %}secondary{% endif %}">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">{{ tache.titre }}</h6>
                                        <small class="text-muted">Priorité {{ tache.priorite }}</small>
                                    </div>
                                    <span class="badge bg-{% if tache.statut == 'TERMINEE' %}success{% elif tache.statut == 'EN_COURS' %}primary{% elif tache.statut == 'EN_RETARD' %}danger{% else %}secondary{% endif %}">
                                        {{ tache.get_statut_display }}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">{{ tache.description|truncatewords:20 }}</p>
                                    
                                    <div class="row text-center mb-2">
                                        <div class="col-6">
                                            <small class="text-muted">Début prévu</small><br>
                                            <strong>{{ tache.date_debut_prevue|date:"d/m/Y" }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Fin prévue</small><br>
                                            <strong>{{ tache.date_fin_prevue|date:"d/m/Y" }}</strong>
                                        </div>
                                    </div>

                                    {% if tache.date_debut_reelle %}
                                    <div class="row text-center mb-2">
                                        <div class="col-6">
                                            <small class="text-success">Début réel</small><br>
                                            <strong class="text-success">{{ tache.date_debut_reelle|date:"d/m/Y" }}</strong>
                                        </div>
                                        {% if tache.date_fin_reelle %}
                                        <div class="col-6">
                                            <small class="text-success">Fin réelle</small><br>
                                            <strong class="text-success">{{ tache.date_fin_reelle|date:"d/m/Y" }}</strong>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}

                                    {% if tache.commentaires %}
                                    <div class="mt-2">
                                        <small class="text-muted">Commentaires :</small>
                                        <p class="small">{{ tache.commentaires|truncatewords:15 }}</p>
                                    </div>
                                    {% endif %}

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            Créée par {{ tache.creee_par.get_full_name }} le {{ tache.date_creation|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        {% if user.role in 'RH,ADMIN,ENCADRANT' and tache.statut != 'TERMINEE' %}
                                        <button class="btn btn-sm btn-outline-primary" onclick="updateTaskStatus({{ tache.id }}, 'EN_COURS')">
                                            <i class="fas fa-play"></i> Démarrer
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="updateTaskStatus({{ tache.id }}, 'TERMINEE')">
                                            <i class="fas fa-check"></i> Terminer
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-info" onclick="showTaskDetails({{ tache.id }})">
                                            <i class="fas fa-eye"></i> Détails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune tâche assignée</h5>
                        <p class="text-muted">Les tâches de stage apparaîtront ici une fois créées.</p>
                        {% if user.role in 'RH,ADMIN,ENCADRANT' %}
                        <a href="{% url 'add_tache' stagiaire.id %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Créer la première tâche
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Boutons d'action -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                        {% if user.role in 'RH,ADMIN,ENCADRANT' %}
                        <a href="{% url 'evaluation_stagiaire' stagiaire.id %}" class="btn btn-warning">
                            <i class="fas fa-star me-1"></i>Évaluer le stagiaire
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails de tâche -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la tâche</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<script>
// Fonction pour obtenir le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function updateTaskStatus(taskId, newStatus) {
    // Afficher un indicateur de chargement
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mise à jour...';
    button.disabled = true;

    // Préparer les données
    const csrftoken = getCookie('csrftoken');
    const data = {
        statut: newStatus
    };

    // Envoyer la requête AJAX
    fetch(`/taches/${taskId}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrftoken,
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Afficher un message de succès
            showAlert('success', data.message);

            // Recharger la page pour mettre à jour l'affichage
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            // Afficher l'erreur
            showAlert('danger', 'Erreur: ' + data.error);

            // Restaurer le bouton
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showAlert('danger', 'Erreur de communication avec le serveur');

        // Restaurer le bouton
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showAlert(type, message) {
    // Créer une alerte Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insérer l'alerte en haut de la page
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Supprimer l'alerte après 5 secondes
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showTaskDetails(taskId) {
    // Ici vous pourriez charger les détails via AJAX
    document.getElementById('taskDetailsContent').innerHTML = `
        <p>Détails de la tâche ${taskId} seraient chargés ici...</p>
    `;
    new bootstrap.Modal(document.getElementById('taskDetailsModal')).show();
}
</script>
{% endblock %}
