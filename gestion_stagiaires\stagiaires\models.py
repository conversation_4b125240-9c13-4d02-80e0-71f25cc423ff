from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
import os


def convention_upload_path(instance, filename):
    """Génère le chemin d'upload pour les conventions de stage"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"convention_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"conventions/{safe_filename}"


def attestation_upload_path(instance, filename):
    """Génère le chemin d'upload pour les attestations de fin de stage"""
    # Nettoie le nom de fichier
    name, ext = os.path.splitext(filename)
    safe_filename = f"attestation_{instance.nom}_{instance.prenom}_{instance.id or 'new'}{ext}"
    return f"attestations/{safe_filename}"
from django.utils import timezone

class CustomUser(AbstractUser):
    ROLE_CHOICES = [
        ('ADMIN', 'Administrateur'),
        ('RH', 'Gestionnaire RH'),
        ('ENCADRANT', 'Encadrant'),
    ]
    role = models.CharField(max_length=10, choices=ROLE_CHOICES)

    class Meta:
        verbose_name = 'Utilisateur personnalisé'
        verbose_name_plural = 'Utilisateurs personnalisés'


class Stagiaire(models.Model):
    """Modèle pour les stagiaires"""

    STATUT_CHOICES = [
        ('EN_COURS', 'En cours'),
        ('TERMINE', 'Terminé'),
        ('SUSPENDU', 'Suspendu'),
        ('ANNULE', 'Annulé'),
    ]

    STATUT_CONVENTION_CHOICES = [
        ('EN_ATTENTE', 'En attente'),
        ('VALIDEE', 'Validée'),
        ('REJETEE', 'Rejetée'),
        ('MODIFIEE', 'À modifier'),
    ]

    STATUT_TACHES_CHOICES = [
        ('NON_COMMENCEES', 'Non commencées'),
        ('EN_COURS', 'En cours'),
        ('PARTIELLEMENT_ACCOMPLIES', 'Partiellement accomplies'),
        ('ACCOMPLIES', 'Accomplies'),
    ]

    DEPARTEMENT_CHOICES = [
        ('IT', 'Informatique'),
        ('MARKETING', 'Marketing'),
        ('RH', 'Ressources Humaines'),
        ('FINANCE', 'Finance'),
        ('COMMERCIAL', 'Commercial'),
        ('PRODUCTION', 'Production'),
    ]

    # Informations personnelles
    nom = models.CharField(max_length=100, verbose_name="Nom")
    prenom = models.CharField(max_length=100, verbose_name="Prénom")
    email = models.EmailField(unique=True, verbose_name="Email")
    telephone = models.CharField(max_length=20, blank=True, verbose_name="Téléphone")
    date_naissance = models.DateField(verbose_name="Date de naissance")

    # Informations du stage
    departement = models.CharField(max_length=20, choices=DEPARTEMENT_CHOICES, verbose_name="Département")
    encadrant = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                 limit_choices_to={'role': 'ENCADRANT'}, verbose_name="Encadrant")
    date_debut = models.DateField(verbose_name="Date de début")
    date_fin = models.DateField(verbose_name="Date de fin")
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_COURS', verbose_name="Statut")

    # Informations académiques
    etablissement = models.CharField(max_length=200, verbose_name="Établissement")
    niveau_etude = models.CharField(max_length=100, verbose_name="Niveau d'étude")
    specialite = models.CharField(max_length=100, verbose_name="Spécialité")

    # Gestion des conventions de stage
    convention_stage = models.FileField(upload_to=convention_upload_path, blank=True, null=True,
                                       verbose_name="Convention de stage")
    statut_convention = models.CharField(max_length=20, choices=STATUT_CONVENTION_CHOICES,
                                       default='EN_ATTENTE', verbose_name="Statut de la convention")
    date_validation_convention = models.DateTimeField(blank=True, null=True,
                                                     verbose_name="Date de validation de la convention")
    validee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='conventions_validees', verbose_name="Validée par")
    commentaire_convention = models.TextField(blank=True, verbose_name="Commentaire sur la convention")

    # Gestion des tâches et attestations
    description_taches = models.TextField(blank=True, verbose_name="Description des tâches à accomplir")
    statut_taches = models.CharField(max_length=30, choices=STATUT_TACHES_CHOICES,
                                   default='NON_COMMENCEES', verbose_name="Statut des tâches")
    evaluation_encadrant = models.TextField(blank=True, verbose_name="Évaluation de l'encadrant")
    note_finale = models.DecimalField(max_digits=4, decimal_places=2, blank=True, null=True,
                                    verbose_name="Note finale (/20)")

    # Attestation de fin de stage
    attestation_fin_stage = models.FileField(upload_to=attestation_upload_path, blank=True, null=True,
                                           verbose_name="Attestation de fin de stage")
    date_generation_attestation = models.DateTimeField(blank=True, null=True,
                                                      verbose_name="Date de génération de l'attestation")
    attestation_generee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True,
                                               related_name='attestations_generees',
                                               verbose_name="Attestation générée par")

    # Métadonnées
    date_creation = models.DateTimeField(default=timezone.now, verbose_name="Date de création")
    cree_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True,
                                related_name='stagiaires_crees', verbose_name="Créé par")

    class Meta:
        verbose_name = 'Stagiaire'
        verbose_name_plural = 'Stagiaires'
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.prenom} {self.nom} - {self.departement}"

    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"

    @property
    def duree_stage(self):
        """Calcule la durée du stage en jours"""
        return (self.date_fin - self.date_debut).days

    @property
    def stage_termine(self):
        """Vérifie si le stage est terminé (date de fin dépassée)"""
        from datetime import date
        return date.today() > self.date_fin

    @property
    def peut_generer_attestation(self):
        """Vérifie si l'attestation peut être générée"""
        return (self.stage_termine and
                self.statut_taches == 'ACCOMPLIES' and
                self.statut_convention == 'VALIDEE')

    @property
    def convention_validee(self):
        """Vérifie si la convention est validée"""
        return self.statut_convention == 'VALIDEE'

    @property
    def taches_accomplies(self):
        """Vérifie si toutes les tâches sont accomplies"""
        return self.statut_taches == 'ACCOMPLIES'

    def peut_valider_convention(self, user):
        """Vérifie si l'utilisateur peut valider la convention"""
        return user.role in ['RH', 'ADMIN'] and self.convention_stage

    def peut_generer_attestation_user(self, user):
        """Vérifie si l'utilisateur peut générer l'attestation"""
        return user.role in ['RH', 'ADMIN'] and self.peut_generer_attestation

class TacheStage(models.Model):
    """Modèle pour les tâches spécifiques d'un stage"""

    STATUT_TACHE_CHOICES = [
        ('NON_COMMENCEE', 'Non commencée'),
        ('EN_COURS', 'En cours'),
        ('TERMINEE', 'Terminée'),
        ('VALIDEE', 'Validée'),
    ]

    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE,
                                 related_name='taches', verbose_name="Stagiaire")
    titre = models.CharField(max_length=200, verbose_name="Titre de la tâche")
    description = models.TextField(verbose_name="Description détaillée")
    date_debut_prevue = models.DateField(verbose_name="Date de début prévue")
    date_fin_prevue = models.DateField(verbose_name="Date de fin prévue")
    date_debut_reelle = models.DateField(blank=True, null=True, verbose_name="Date de début réelle")
    date_fin_reelle = models.DateField(blank=True, null=True, verbose_name="Date de fin réelle")
    statut = models.CharField(max_length=20, choices=STATUT_TACHE_CHOICES,
                             default='NON_COMMENCEE', verbose_name="Statut")
    priorite = models.IntegerField(default=1, verbose_name="Priorité (1=haute, 5=basse)")
    commentaire_stagiaire = models.TextField(blank=True, verbose_name="Commentaire du stagiaire")
    commentaire_encadrant = models.TextField(blank=True, verbose_name="Commentaire de l'encadrant")
    note = models.DecimalField(max_digits=4, decimal_places=2, blank=True, null=True,
                              verbose_name="Note (/20)")

    # Métadonnées
    date_creation = models.DateTimeField(default=timezone.now, verbose_name="Date de création")
    creee_par = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True,
                                 verbose_name="Créée par")

    class Meta:
        verbose_name = 'Tâche de stage'
        verbose_name_plural = 'Tâches de stage'
        ordering = ['priorite', 'date_debut_prevue']

    def __str__(self):
        return f"{self.titre} - {self.stagiaire.nom_complet}"

    @property
    def duree_prevue(self):
        """Calcule la durée prévue en jours"""
        return (self.date_fin_prevue - self.date_debut_prevue).days

    @property
    def duree_reelle(self):
        """Calcule la durée réelle en jours"""
        if self.date_debut_reelle and self.date_fin_reelle:
            return (self.date_fin_reelle - self.date_debut_reelle).days
        return None

    @property
    def en_retard(self):
        """Vérifie si la tâche est en retard"""
        from datetime import date
        return (self.statut != 'TERMINEE' and
                self.statut != 'VALIDEE' and
                date.today() > self.date_fin_prevue)


def rapport_upload_path(instance, filename):
    """Fonction pour définir le chemin d'upload des rapports"""
    return f'rapports/{instance.stagiaire.id}/{filename}'


class Mission(models.Model):
    """Modèle pour les missions assignées aux stagiaires"""

    STATUT_MISSION_CHOICES = [
        ('PLANIFIEE', 'Planifiée'),
        ('EN_COURS', 'En cours'),
        ('TERMINEE', 'Terminée'),
        ('VALIDEE', 'Validée'),
        ('REJETEE', 'Rejetée'),
    ]

    PRIORITE_CHOICES = [
        (1, 'Très haute'),
        (2, 'Haute'),
        (3, 'Moyenne'),
        (4, 'Basse'),
        (5, 'Très basse'),
    ]

    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE, related_name='missions')
    titre = models.CharField(max_length=200, verbose_name="Titre de la mission")
    description = models.TextField(verbose_name="Description détaillée")
    objectifs = models.TextField(verbose_name="Objectifs à atteindre")
    livrables_attendus = models.TextField(verbose_name="Livrables attendus")

    date_debut_prevue = models.DateField(verbose_name="Date de début prévue")
    date_fin_prevue = models.DateField(verbose_name="Date de fin prévue")
    date_debut_reelle = models.DateField(null=True, blank=True, verbose_name="Date de début réelle")
    date_fin_reelle = models.DateField(null=True, blank=True, verbose_name="Date de fin réelle")

    priorite = models.IntegerField(choices=PRIORITE_CHOICES, default=3, verbose_name="Priorité")
    statut = models.CharField(max_length=20, choices=STATUT_MISSION_CHOICES, default='PLANIFIEE', verbose_name="Statut")

    # Suivi d'avancement
    pourcentage_avancement = models.IntegerField(default=0, verbose_name="Pourcentage d'avancement")
    commentaire_avancement = models.TextField(blank=True, verbose_name="Commentaire sur l'avancement")
    derniere_mise_a_jour = models.DateTimeField(auto_now=True, verbose_name="Dernière mise à jour")

    # Métadonnées
    creee_par = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='missions_creees')
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")

    class Meta:
        verbose_name = "Mission"
        verbose_name_plural = "Missions"
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.titre} - {self.stagiaire.nom_complet}"

    @property
    def duree_prevue(self):
        """Calcule la durée prévue en jours"""
        if self.date_debut_prevue and self.date_fin_prevue:
            return (self.date_fin_prevue - self.date_debut_prevue).days + 1
        return 0

    @property
    def duree_reelle(self):
        """Calcule la durée réelle en jours"""
        if self.date_debut_reelle and self.date_fin_reelle:
            return (self.date_fin_reelle - self.date_debut_reelle).days + 1
        return 0

    @property
    def en_retard(self):
        """Vérifie si la mission est en retard"""
        from django.utils import timezone
        return (self.date_fin_prevue < timezone.now().date() and
                self.statut not in ['TERMINEE', 'VALIDEE'])


class RapportStage(models.Model):
    """Modèle pour les rapports de stage"""

    STATUT_RAPPORT_CHOICES = [
        ('BROUILLON', 'Brouillon'),
        ('SOUMIS', 'Soumis'),
        ('EN_REVISION', 'En révision'),
        ('VALIDE', 'Validé'),
        ('REJETE', 'Rejeté'),
    ]

    stagiaire = models.ForeignKey(Stagiaire, on_delete=models.CASCADE, related_name='rapports')
    mission = models.ForeignKey(Mission, on_delete=models.CASCADE, related_name='rapports', null=True, blank=True)

    titre = models.CharField(max_length=200, verbose_name="Titre du rapport")
    fichier_rapport = models.FileField(upload_to=rapport_upload_path, verbose_name="Fichier du rapport")
    description = models.TextField(verbose_name="Description du contenu")

    statut = models.CharField(max_length=20, choices=STATUT_RAPPORT_CHOICES, default='BROUILLON', verbose_name="Statut")

    # Validation par l'encadrant
    date_soumission = models.DateTimeField(null=True, blank=True, verbose_name="Date de soumission")
    date_validation = models.DateTimeField(null=True, blank=True, verbose_name="Date de validation")
    valide_par = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='rapports_valides', null=True, blank=True)

    commentaires_encadrant = models.TextField(blank=True, verbose_name="Commentaires de l'encadrant")
    note_rapport = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True, verbose_name="Note du rapport")

    # Métadonnées
    date_creation = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    date_modification = models.DateTimeField(auto_now=True, verbose_name="Date de modification")

    class Meta:
        verbose_name = "Rapport de stage"
        verbose_name_plural = "Rapports de stage"
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.titre} - {self.stagiaire.nom_complet}"

    def save(self, *args, **kwargs):
        # Mettre à jour la date de soumission si le statut change vers SOUMIS
        if self.statut == 'SOUMIS' and not self.date_soumission:
            from django.utils import timezone
            self.date_soumission = timezone.now()

        # Mettre à jour la date de validation si le statut change vers VALIDE
        if self.statut == 'VALIDE' and not self.date_validation:
            from django.utils import timezone
            self.date_validation = timezone.now()

        super().save(*args, **kwargs)


# Create your models here.
