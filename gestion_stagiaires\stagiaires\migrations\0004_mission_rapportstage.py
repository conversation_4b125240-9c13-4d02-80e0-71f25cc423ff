# Generated by Django 5.1.6 on 2025-07-01 21:16

import django.db.models.deletion
import stagiaires.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0003_stagiaire_attestation_fin_stage_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Mission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre de la mission')),
                ('description', models.TextField(verbose_name='Description détaillée')),
                ('objectifs', models.TextField(verbose_name='Objectifs à atteindre')),
                ('livrables_attendus', models.TextField(verbose_name='Livrables attendus')),
                ('date_debut_prevue', models.DateField(verbose_name='Date de début prévue')),
                ('date_fin_prevue', models.DateField(verbose_name='Date de fin prévue')),
                ('date_debut_reelle', models.DateField(blank=True, null=True, verbose_name='Date de début réelle')),
                ('date_fin_reelle', models.DateField(blank=True, null=True, verbose_name='Date de fin réelle')),
                ('priorite', models.IntegerField(choices=[(1, 'Très haute'), (2, 'Haute'), (3, 'Moyenne'), (4, 'Basse'), (5, 'Très basse')], default=3, verbose_name='Priorité')),
                ('statut', models.CharField(choices=[('PLANIFIEE', 'Planifiée'), ('EN_COURS', 'En cours'), ('TERMINEE', 'Terminée'), ('VALIDEE', 'Validée'), ('REJETEE', 'Rejetée')], default='PLANIFIEE', max_length=20, verbose_name='Statut')),
                ('pourcentage_avancement', models.IntegerField(default=0, verbose_name="Pourcentage d'avancement")),
                ('commentaire_avancement', models.TextField(blank=True, verbose_name="Commentaire sur l'avancement")),
                ('derniere_mise_a_jour', models.DateTimeField(auto_now=True, verbose_name='Dernière mise à jour')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('creee_par', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missions_creees', to=settings.AUTH_USER_MODEL)),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missions', to='stagiaires.stagiaire')),
            ],
            options={
                'verbose_name': 'Mission',
                'verbose_name_plural': 'Missions',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='RapportStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre du rapport')),
                ('fichier_rapport', models.FileField(upload_to=stagiaires.models.rapport_upload_path, verbose_name='Fichier du rapport')),
                ('description', models.TextField(verbose_name='Description du contenu')),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('SOUMIS', 'Soumis'), ('EN_REVISION', 'En révision'), ('VALIDE', 'Validé'), ('REJETE', 'Rejeté')], default='BROUILLON', max_length=20, verbose_name='Statut')),
                ('date_soumission', models.DateTimeField(blank=True, null=True, verbose_name='Date de soumission')),
                ('date_validation', models.DateTimeField(blank=True, null=True, verbose_name='Date de validation')),
                ('commentaires_encadrant', models.TextField(blank=True, verbose_name="Commentaires de l'encadrant")),
                ('note_rapport', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Note du rapport')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('mission', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rapports', to='stagiaires.mission')),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rapports', to='stagiaires.stagiaire')),
                ('valide_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rapports_valides', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Rapport de stage',
                'verbose_name_plural': 'Rapports de stage',
                'ordering': ['-date_creation'],
            },
        ),
    ]
