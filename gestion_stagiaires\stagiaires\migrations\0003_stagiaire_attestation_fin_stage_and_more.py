# Generated by Django 5.1.6 on 2025-07-01 20:46

import django.db.models.deletion
import django.utils.timezone
import stagiaires.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0002_stagiaire'),
    ]

    operations = [
        migrations.AddField(
            model_name='stagiaire',
            name='attestation_fin_stage',
            field=models.FileField(blank=True, null=True, upload_to=stagiaires.models.attestation_upload_path, verbose_name='Attestation de fin de stage'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='attestation_generee_par',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attestations_generees', to=settings.AUTH_USER_MODEL, verbose_name='Attestation générée par'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='commentaire_convention',
            field=models.TextField(blank=True, verbose_name='Commentaire sur la convention'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='convention_stage',
            field=models.FileField(blank=True, null=True, upload_to=stagiaires.models.convention_upload_path, verbose_name='Convention de stage'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='date_generation_attestation',
            field=models.DateTimeField(blank=True, null=True, verbose_name="Date de génération de l'attestation"),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='date_validation_convention',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Date de validation de la convention'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='description_taches',
            field=models.TextField(blank=True, verbose_name='Description des tâches à accomplir'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='evaluation_encadrant',
            field=models.TextField(blank=True, verbose_name="Évaluation de l'encadrant"),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='note_finale',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Note finale (/20)'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='statut_convention',
            field=models.CharField(choices=[('EN_ATTENTE', 'En attente'), ('VALIDEE', 'Validée'), ('REJETEE', 'Rejetée'), ('MODIFIEE', 'À modifier')], default='EN_ATTENTE', max_length=20, verbose_name='Statut de la convention'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='statut_taches',
            field=models.CharField(choices=[('NON_COMMENCEES', 'Non commencées'), ('EN_COURS', 'En cours'), ('PARTIELLEMENT_ACCOMPLIES', 'Partiellement accomplies'), ('ACCOMPLIES', 'Accomplies')], default='NON_COMMENCEES', max_length=30, verbose_name='Statut des tâches'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='validee_par',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conventions_validees', to=settings.AUTH_USER_MODEL, verbose_name='Validée par'),
        ),
        migrations.CreateModel(
            name='TacheStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre de la tâche')),
                ('description', models.TextField(verbose_name='Description détaillée')),
                ('date_debut_prevue', models.DateField(verbose_name='Date de début prévue')),
                ('date_fin_prevue', models.DateField(verbose_name='Date de fin prévue')),
                ('date_debut_reelle', models.DateField(blank=True, null=True, verbose_name='Date de début réelle')),
                ('date_fin_reelle', models.DateField(blank=True, null=True, verbose_name='Date de fin réelle')),
                ('statut', models.CharField(choices=[('NON_COMMENCEE', 'Non commencée'), ('EN_COURS', 'En cours'), ('TERMINEE', 'Terminée'), ('VALIDEE', 'Validée')], default='NON_COMMENCEE', max_length=20, verbose_name='Statut')),
                ('priorite', models.IntegerField(default=1, verbose_name='Priorité (1=haute, 5=basse)')),
                ('commentaire_stagiaire', models.TextField(blank=True, verbose_name='Commentaire du stagiaire')),
                ('commentaire_encadrant', models.TextField(blank=True, verbose_name="Commentaire de l'encadrant")),
                ('note', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Note (/20)')),
                ('date_creation', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date de création')),
                ('creee_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Créée par')),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='taches', to='stagiaires.stagiaire', verbose_name='Stagiaire')),
            ],
            options={
                'verbose_name': 'Tâche de stage',
                'verbose_name_plural': 'Tâches de stage',
                'ordering': ['priorite', 'date_debut_prevue'],
            },
        ),
    ]
