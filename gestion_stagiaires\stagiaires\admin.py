from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import Custom<PERSON>ser, Stagiaire, TacheStage, Mission, RapportStage

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'is_staff')
    list_filter = ('role', 'is_staff', 'is_superuser', 'is_active')
    fieldsets = UserAdmin.fieldsets + (
        ('Informations supplémentaires', {'fields': ('role',)}),
    )
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Informations supplémentaires', {'fields': ('role',)}),
    )

class TacheStageInline(admin.TabularInline):
    """Inline pour les tâches de stage"""
    model = TacheStage
    extra = 1
    fields = ('titre', 'date_debut_prevue', 'date_fin_prevue', 'statut', 'priorite')
    readonly_fields = ('date_creation',)


class MissionInline(admin.TabularInline):
    """Inline pour les missions"""
    model = Mission
    extra = 0
    fields = ('titre', 'date_debut_prevue', 'date_fin_prevue', 'statut', 'priorite', 'pourcentage_avancement')
    readonly_fields = ('date_creation',)


class RapportStageInline(admin.TabularInline):
    """Inline pour les rapports de stage"""
    model = RapportStage
    extra = 0
    fields = ('titre', 'mission', 'statut', 'note_rapport')
    readonly_fields = ('date_creation', 'date_soumission')


@admin.register(Stagiaire)
class StagiaireAdmin(admin.ModelAdmin):
    list_display = ('nom_complet', 'email', 'departement', 'encadrant', 'statut',
                   'statut_convention', 'statut_taches', 'date_debut', 'date_fin')
    list_filter = ('statut', 'statut_convention', 'statut_taches', 'departement',
                  'date_debut', 'date_fin')
    search_fields = ('nom', 'prenom', 'email', 'etablissement')
    readonly_fields = ('date_creation', 'stage_termine', 'peut_generer_attestation')
    inlines = [TacheStageInline, MissionInline, RapportStageInline]

    fieldsets = (
        ('Informations personnelles', {
            'fields': ('nom', 'prenom', 'email', 'telephone', 'date_naissance')
        }),
        ('Informations du stage', {
            'fields': ('departement', 'encadrant', 'date_debut', 'date_fin', 'statut')
        }),
        ('Informations académiques', {
            'fields': ('etablissement', 'niveau_etude', 'specialite')
        }),
        ('Convention de stage', {
            'fields': ('convention_stage', 'statut_convention', 'date_validation_convention',
                      'validee_par', 'commentaire_convention')
        }),
        ('Tâches et évaluation', {
            'fields': ('description_taches', 'statut_taches', 'evaluation_encadrant', 'note_finale')
        }),
        ('Attestation', {
            'fields': ('attestation_fin_stage', 'date_generation_attestation', 'attestation_generee_par')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'cree_par', 'stage_termine', 'peut_generer_attestation'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouveau stagiaire
            obj.cree_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(TacheStage)
class TacheStageAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'statut', 'priorite', 'date_debut_prevue',
                   'date_fin_prevue', 'en_retard')
    list_filter = ('statut', 'priorite', 'date_debut_prevue', 'date_fin_prevue')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'duree_prevue', 'duree_reelle', 'en_retard')

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'titre', 'description', 'priorite')
        }),
        ('Planification', {
            'fields': ('date_debut_prevue', 'date_fin_prevue', 'date_debut_reelle', 'date_fin_reelle')
        }),
        ('Suivi', {
            'fields': ('statut', 'commentaire_stagiaire', 'commentaire_encadrant', 'note')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'creee_par', 'duree_prevue', 'duree_reelle', 'en_retard'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouvelle tâche
            obj.creee_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(Mission)
class MissionAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'statut', 'priorite', 'pourcentage_avancement',
                   'date_debut_prevue', 'date_fin_prevue', 'en_retard')
    list_filter = ('statut', 'priorite', 'date_debut_prevue', 'date_fin_prevue', 'creee_par')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'duree_prevue', 'duree_reelle', 'en_retard', 'derniere_mise_a_jour')

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'titre', 'description', 'priorite', 'creee_par')
        }),
        ('Objectifs et livrables', {
            'fields': ('objectifs', 'livrables_attendus')
        }),
        ('Planification', {
            'fields': ('date_debut_prevue', 'date_fin_prevue', 'date_debut_reelle', 'date_fin_reelle')
        }),
        ('Suivi', {
            'fields': ('statut', 'pourcentage_avancement', 'commentaire_avancement')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'derniere_mise_a_jour', 'duree_prevue', 'duree_reelle', 'en_retard'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouvelle mission
            obj.creee_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(RapportStage)
class RapportStageAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'mission', 'statut', 'note_rapport',
                   'date_soumission', 'valide_par')
    list_filter = ('statut', 'date_soumission', 'date_validation', 'valide_par')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'date_soumission', 'date_validation')

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'mission', 'titre', 'description')
        }),
        ('Fichier', {
            'fields': ('fichier_rapport',)
        }),
        ('Statut et validation', {
            'fields': ('statut', 'valide_par', 'date_validation', 'note_rapport', 'commentaires_validation')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'date_soumission'),
            'classes': ('collapse',)
        }),
    )


# Register your models here.
