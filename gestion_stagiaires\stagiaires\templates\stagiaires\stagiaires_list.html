{% extends 'stagiaires/base.html' %}

{% block title %}Liste des Stagiaires - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Liste des Stagiaires
                    </h3>
                    <a href="{% url 'dashboard' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                    </a>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Rechercher un stagiaire...">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{% url 'add_stagiaire' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-user me-1"></i>Nom</th>
                                    <th><i class="fas fa-envelope me-1"></i>Email</th>
                                    <th><i class="fas fa-building me-1"></i>Département</th>
                                    <th><i class="fas fa-user-tie me-1"></i>Encadrant</th>
                                    <th><i class="fas fa-calendar me-1"></i>Période</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if stagiaires %}
                                    {% for stagiaire in stagiaires %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                                </div>
                                                <div>
                                                    <strong>{{ stagiaire.nom_complet }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ stagiaire.specialite }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ stagiaire.email }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ stagiaire.get_departement_display }}</span>
                                        </td>
                                        <td>
                                            {% if stagiaire.encadrant %}
                                                {{ stagiaire.encadrant.get_full_name|default:stagiaire.encadrant.username }}
                                            {% else %}
                                                <span class="text-muted">Non assigné</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</small>
                                            <br>
                                            <span class="badge bg-info">{{ stagiaire.duree_stage }} jours</span>
                                        </td>
                                        <td>
                                            {% if stagiaire.statut == 'EN_COURS' %}
                                                <span class="badge bg-success">{{ stagiaire.get_statut_display }}</span>
                                            {% elif stagiaire.statut == 'TERMINE' %}
                                                <span class="badge bg-secondary">{{ stagiaire.get_statut_display }}</span>
                                            {% elif stagiaire.statut == 'SUSPENDU' %}
                                                <span class="badge bg-warning">{{ stagiaire.get_statut_display }}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{{ stagiaire.get_statut_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{% url 'taches_stagiaire' stagiaire.id %}" class="btn btn-outline-primary" title="Voir tâches">
                                                    <i class="fas fa-tasks"></i>
                                                </a>
                                                {% if user.role in 'RH,ADMIN' %}
                                                <a href="{% url 'convention_upload' stagiaire.id %}" class="btn btn-outline-success" title="Convention">
                                                    <i class="fas fa-file-contract"></i>
                                                </a>
                                                {% endif %}
                                                {% if user.role in 'RH,ADMIN,ENCADRANT' %}
                                                <a href="{% url 'evaluation_stagiaire' stagiaire.id %}" class="btn btn-outline-warning" title="Évaluer">
                                                    <i class="fas fa-star"></i>
                                                </a>
                                                {% endif %}
                                                {% if user.role in 'RH,ADMIN' and stagiaire.peut_generer_attestation %}
                                                <a href="{% url 'generer_attestation' stagiaire.id %}" class="btn btn-outline-info" title="Attestation">
                                                    <i class="fas fa-certificate"></i>
                                                </a>
                                                {% endif %}
                                                {% if user.role == 'ADMIN' %}
                                                <button class="btn btn-outline-danger" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>Aucun stagiaire trouvé</h5>
                                                <p>Commencez par ajouter votre premier stagiaire.</p>
                                                <a href="{% url 'add_stagiaire' %}" class="btn btn-success">
                                                    <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    {% if stagiaires %}
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Total : {{ stagiaires|length }} stagiaire{{ stagiaires|length|pluralize }}
                            </small>
                        </div>
                        <div>
                            <a href="{% url 'add_stagiaire' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
{% endblock %}
