{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter Tâche - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        Ajouter une Tâche de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user me-2"></i>Informations du stage</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Stagiaire :</strong> {{ stagiaire.nom_complet }}<br>
                                <strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}<br>
                                <strong>Département :</strong> {{ stagiaire.get_departement_display }}
                            </div>
                            <div class="col-md-6">
                                <strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}<br>
                                <strong>Durée :</strong> {{ stagiaire.duree_stage }} jours<br>
                                <strong>Statut :</strong> 
                                <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}primary{% elif stagiaire.statut == 'TERMINE' %}success{% else %}secondary{% endif %}">
                                    {{ stagiaire.get_statut_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire d'ajout de tâche -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.titre.id_for_label }}" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Titre de la tâche *
                                </label>
                                {{ form.titre }}
                                {% if form.titre.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.titre.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.priorite.id_for_label }}" class="form-label">
                                    <i class="fas fa-exclamation me-1"></i>Priorité
                                </label>
                                {{ form.priorite }}
                                {% if form.priorite.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.priorite.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">1 = Très haute, 5 = Très basse</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description détaillée *
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Décrivez précisément ce que le stagiaire doit accomplir, les objectifs et les livrables attendus.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_debut_prevue.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-plus me-1"></i>Date de début prévue *
                                </label>
                                {{ form.date_debut_prevue }}
                                {% if form.date_debut_prevue.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.date_debut_prevue.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Entre {{ stagiaire.date_debut|date:"d/m/Y" }} et {{ stagiaire.date_fin|date:"d/m/Y" }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>Date de fin prévue *
                                </label>
                                {{ form.date_fin_prevue }}
                                {% if form.date_fin_prevue.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.date_fin_prevue.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Doit être après la date de début
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.statut.id_for_label }}" class="form-label">
                                <i class="fas fa-flag me-1"></i>Statut initial
                            </label>
                            {{ form.statut }}
                            {% if form.statut.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.statut.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Généralement "À faire" pour une nouvelle tâche
                            </div>
                        </div>

                        <!-- Conseils pour la création de tâches -->
                        <div class="card border-info mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Conseils pour créer une bonne tâche</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li><strong>Titre clair :</strong> Utilisez un titre descriptif et concis</li>
                                    <li><strong>Description SMART :</strong> Spécifique, Mesurable, Atteignable, Réaliste, Temporellement défini</li>
                                    <li><strong>Priorité :</strong> Alignez la priorité avec les objectifs du stage</li>
                                    <li><strong>Durée réaliste :</strong> Prévoyez suffisamment de temps pour l'apprentissage</li>
                                    <li><strong>Livrables :</strong> Précisez ce qui est attendu à la fin de la tâche</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Exemples de tâches -->
                        <div class="card border-secondary mb-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-examples me-2"></i>Exemples de tâches par département</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Informatique :</strong>
                                        <ul class="small">
                                            <li>Développer un module de connexion</li>
                                            <li>Rédiger la documentation technique</li>
                                            <li>Effectuer des tests unitaires</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Marketing :</strong>
                                        <ul class="small">
                                            <li>Analyser la concurrence</li>
                                            <li>Créer du contenu pour les réseaux sociaux</li>
                                            <li>Préparer une présentation client</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'taches_stagiaire' stagiaire.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour aux tâches
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Créer la tâche
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validation côté client pour les dates
document.addEventListener('DOMContentLoaded', function() {
    const dateDebut = document.getElementById('{{ form.date_debut_prevue.id_for_label }}');
    const dateFin = document.getElementById('{{ form.date_fin_prevue.id_for_label }}');
    
    // Définir les limites de dates basées sur la période de stage
    const stageDebut = '{{ stagiaire.date_debut|date:"Y-m-d" }}';
    const stageFin = '{{ stagiaire.date_fin|date:"Y-m-d" }}';
    
    if (dateDebut) {
        dateDebut.min = stageDebut;
        dateDebut.max = stageFin;
    }
    
    if (dateFin) {
        dateFin.min = stageDebut;
        dateFin.max = stageFin;
    }
    
    // Validation que la date de fin est après la date de début
    function validateDates() {
        if (dateDebut.value && dateFin.value) {
            if (new Date(dateFin.value) <= new Date(dateDebut.value)) {
                dateFin.setCustomValidity('La date de fin doit être postérieure à la date de début');
            } else {
                dateFin.setCustomValidity('');
            }
        }
    }
    
    if (dateDebut) dateDebut.addEventListener('change', validateDates);
    if (dateFin) dateFin.addEventListener('change', validateDates);
});
</script>
{% endblock %}
