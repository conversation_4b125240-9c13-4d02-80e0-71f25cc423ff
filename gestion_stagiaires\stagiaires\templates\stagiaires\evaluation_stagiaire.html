{% extends 'stagiaires/base.html' %}

{% block title %}Évaluation - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        Évaluation de Fin de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p><strong>Établissement :</strong> {{ stagiaire.etablissement }}</p>
                                    <p><strong>Niveau :</strong> {{ stagiaire.niveau_etude }}</p>
                                    <p><strong>Spécialité :</strong> {{ stagiaire.specialite }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Détails du stage</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                    <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                    <p><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                    <p><strong>Durée :</strong> {{ stagiaire.duree_stage }} jours</p>
                                    <p><strong>Statut :</strong> 
                                        <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}primary{% elif stagiaire.statut == 'TERMINE' %}success{% else %}secondary{% endif %}">
                                            {{ stagiaire.get_statut_display }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Résumé des tâches -->
                    <div class="card border-secondary mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Résumé des tâches accomplies</h6>
                        </div>
                        <div class="card-body">
                            {% if stagiaire.taches.all %}
                                <div class="row">
                                    {% for tache in stagiaire.taches.all %}
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-{% if tache.statut == 'TERMINEE' %}check-circle text-success{% elif tache.statut == 'EN_COURS' %}clock text-primary{% else %}circle text-muted{% endif %} me-2"></i>
                                            <div>
                                                <strong>{{ tache.titre }}</strong><br>
                                                <small class="text-muted">{{ tache.get_statut_display }} - Priorité {{ tache.priorite }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">Aucune tâche assignée à ce stagiaire.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Formulaire d'évaluation -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.statut_taches.id_for_label }}" class="form-label">
                                    <i class="fas fa-clipboard-check me-1"></i>Statut global des tâches *
                                </label>
                                {{ form.statut_taches }}
                                {% if form.statut_taches.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.statut_taches.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Évaluez l'accomplissement global des tâches assignées
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.note_finale.id_for_label }}" class="form-label">
                                    <i class="fas fa-graduation-cap me-1"></i>Note finale (sur 20)
                                </label>
                                {{ form.note_finale }}
                                {% if form.note_finale.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.note_finale.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Note globale du stage (0 à 20)
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.evaluation_encadrant.id_for_label }}" class="form-label">
                                <i class="fas fa-comment-alt me-1"></i>Évaluation détaillée de l'encadrant *
                            </label>
                            {{ form.evaluation_encadrant }}
                            {% if form.evaluation_encadrant.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.evaluation_encadrant.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Rédigez une évaluation complète du stagiaire incluant ses points forts, axes d'amélioration, et recommandations.
                            </div>
                        </div>

                        <!-- Guide d'évaluation -->
                        <div class="card border-info mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guide d'évaluation</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Critères à évaluer :</h6>
                                        <ul class="small">
                                            <li>Qualité du travail fourni</li>
                                            <li>Respect des délais</li>
                                            <li>Autonomie et initiative</li>
                                            <li>Capacité d'apprentissage</li>
                                            <li>Communication et collaboration</li>
                                            <li>Assiduité et ponctualité</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Barème de notation :</h6>
                                        <ul class="small">
                                            <li><strong>18-20 :</strong> Excellent, dépasse les attentes</li>
                                            <li><strong>16-17 :</strong> Très bien, atteint pleinement les objectifs</li>
                                            <li><strong>14-15 :</strong> Bien, atteint la plupart des objectifs</li>
                                            <li><strong>12-13 :</strong> Satisfaisant, atteint les objectifs de base</li>
                                            <li><strong>10-11 :</strong> Passable, quelques lacunes</li>
                                            <li><strong>0-9 :</strong> Insuffisant, objectifs non atteints</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Évaluation précédente (si elle existe) -->
                        {% if stagiaire.evaluation_encadrant %}
                        <div class="card border-warning mb-3">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-history me-2"></i>Évaluation actuelle</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <p><strong>Statut des tâches :</strong> 
                                            <span class="badge bg-{% if stagiaire.statut_taches == 'ACCOMPLIES' %}success{% elif stagiaire.statut_taches == 'PARTIELLEMENT_ACCOMPLIES' %}warning{% else %}danger{% endif %}">
                                                {{ stagiaire.get_statut_taches_display }}
                                            </span>
                                        </p>
                                        <p><strong>Évaluation :</strong></p>
                                        <div class="alert alert-light">{{ stagiaire.evaluation_encadrant|linebreaks }}</div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h3>
                                            <span class="badge bg-{% if stagiaire.note_finale >= 16 %}success{% elif stagiaire.note_finale >= 12 %}warning{% else %}danger{% endif %} fs-4">
                                                {{ stagiaire.note_finale|default:"--" }}/20
                                            </span>
                                        </h3>
                                        <small class="text-muted">Note actuelle</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Enregistrer l'évaluation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validation de la note
document.addEventListener('DOMContentLoaded', function() {
    const noteInput = document.getElementById('{{ form.note_finale.id_for_label }}');
    
    if (noteInput) {
        noteInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value < 0 || value > 20) {
                this.setCustomValidity('La note doit être comprise entre 0 et 20');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>
{% endblock %}
