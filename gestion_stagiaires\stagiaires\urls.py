from django.urls import path
from . import views

urlpatterns = [
    path('', views.dashboard_view, name='dashboard'),
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('logout/', views.logout_view, name='logout'),

    # Gestion des stagiaires
    path('stagiaires/', views.stagiaires_list_view, name='stagiaires_list'),
    path('stagiaires/add/', views.add_stagiaire_view, name='add_stagiaire'),

    # Rapports et statistiques
    path('reports/', views.reports_view, name='reports'),
    path('export/', views.export_view, name='export'),

    # Fonctions RH
    path('users/', views.user_management_view, name='user_management'),
    path('users/<int:user_id>/delete/', views.delete_user_view, name='delete_user'),
    path('users/<int:user_id>/toggle-status/', views.toggle_user_status_view, name='toggle_user_status'),
    path('contracts/', views.contracts_view, name='contracts'),

    # Gestion des conventions
    path('conventions/', views.conventions_list_view, name='conventions_list'),
    path('stagiaires/<int:stagiaire_id>/convention/upload/', views.convention_upload_view, name='convention_upload'),
    path('stagiaires/<int:stagiaire_id>/convention/validate/', views.convention_validation_view, name='convention_validation'),

    # Gestion des tâches
    path('stagiaires/<int:stagiaire_id>/taches/', views.taches_stagiaire_view, name='taches_stagiaire'),
    path('stagiaires/<int:stagiaire_id>/taches/add/', views.add_tache_view, name='add_tache'),
    path('taches/<int:tache_id>/update-status/', views.update_tache_status, name='update_tache_status'),

    # Évaluations
    path('stagiaires/<int:stagiaire_id>/evaluation/', views.evaluation_stagiaire_view, name='evaluation_stagiaire'),

    # Attestations
    path('attestations/', views.attestations_list_view, name='attestations_list'),
    path('stagiaires/<int:stagiaire_id>/attestation/generer/', views.generer_attestation_view, name='generer_attestation'),

    # Gestion des missions (Encadrants)
    path('stagiaires/<int:stagiaire_id>/missions/', views.missions_stagiaire_view, name='missions_stagiaire'),
    path('stagiaires/<int:stagiaire_id>/missions/planifier/', views.planifier_mission_view, name='planifier_mission'),
    path('missions/<int:mission_id>/suivi/', views.suivi_mission_view, name='suivi_mission'),

    # Gestion des rapports
    path('stagiaires/<int:stagiaire_id>/rapports/', views.rapports_stagiaire_view, name='rapports_stagiaire'),
    path('stagiaires/<int:stagiaire_id>/rapports/soumettre/', views.soumettre_rapport_view, name='soumettre_rapport'),
    path('rapports/<int:rapport_id>/valider/', views.valider_rapport_view, name='valider_rapport'),
]
