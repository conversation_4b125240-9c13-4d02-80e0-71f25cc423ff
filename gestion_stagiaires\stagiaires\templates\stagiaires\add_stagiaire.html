{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un Stagiaire - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Ajouter un Nouveau Stagiaire
                    </h3>
                    <div>
                        <a href="{% url 'stagiaires_list' %}" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-list me-1"></i>Liste des stagiaires
                        </a>
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Tableau de bord
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="stagiaireForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Informations personnelles -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary">
                                    <i class="fas fa-user me-2"></i>
                                    Informations personnelles
                                </h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.prenom.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>Prénom *
                                    </label>
                                    {{ form.prenom }}
                                    {% if form.prenom.errors %}
                                        <div class="text-danger small mt-1">{{ form.prenom.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.nom.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>Nom *
                                    </label>
                                    {{ form.nom }}
                                    {% if form.nom.errors %}
                                        <div class="text-danger small mt-1">{{ form.nom.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email *
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small mt-1">{{ form.email.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.telephone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>Téléphone
                                    </label>
                                    {{ form.telephone }}
                                    {% if form.telephone.errors %}
                                        <div class="text-danger small mt-1">{{ form.telephone.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_naissance.id_for_label }}" class="form-label">
                                        <i class="fas fa-birthday-cake me-1"></i>Date de naissance *
                                    </label>
                                    {{ form.date_naissance }}
                                    {% if form.date_naissance.errors %}
                                        <div class="text-danger small mt-1">{{ form.date_naissance.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Informations du stage -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-info">
                                    <i class="fas fa-briefcase me-2"></i>
                                    Informations du stage
                                </h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.departement.id_for_label }}" class="form-label">
                                        <i class="fas fa-building me-1"></i>Département *
                                    </label>
                                    {{ form.departement }}
                                    {% if form.departement.errors %}
                                        <div class="text-danger small mt-1">{{ form.departement.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.encadrant.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>Encadrant
                                    </label>
                                    {{ form.encadrant }}
                                    {% if form.encadrant.errors %}
                                        <div class="text-danger small mt-1">{{ form.encadrant.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_debut.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>Date de début *
                                    </label>
                                    {{ form.date_debut }}
                                    {% if form.date_debut.errors %}
                                        <div class="text-danger small mt-1">{{ form.date_debut.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_fin.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-check me-1"></i>Date de fin *
                                    </label>
                                    {{ form.date_fin }}
                                    {% if form.date_fin.errors %}
                                        <div class="text-danger small mt-1">{{ form.date_fin.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.statut.id_for_label }}" class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>Statut
                                    </label>
                                    {{ form.statut }}
                                    {% if form.statut.errors %}
                                        <div class="text-danger small mt-1">{{ form.statut.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Informations académiques -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-warning">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Informations académiques
                                </h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.etablissement.id_for_label }}" class="form-label">
                                        <i class="fas fa-university me-1"></i>Établissement *
                                    </label>
                                    {{ form.etablissement }}
                                    {% if form.etablissement.errors %}
                                        <div class="text-danger small mt-1">{{ form.etablissement.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.niveau_etude.id_for_label }}" class="form-label">
                                        <i class="fas fa-layer-group me-1"></i>Niveau d'étude *
                                    </label>
                                    {{ form.niveau_etude }}
                                    {% if form.niveau_etude.errors %}
                                        <div class="text-danger small mt-1">{{ form.niveau_etude.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.specialite.id_for_label }}" class="form-label">
                                        <i class="fas fa-star me-1"></i>Spécialité *
                                    </label>
                                    {{ form.specialite }}
                                    {% if form.specialite.errors %}
                                        <div class="text-danger small mt-1">{{ form.specialite.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>Enregistrer le stagiaire
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation des dates
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    
    dateDebut.addEventListener('change', function() {
        dateFin.min = this.value;
    });
    
    dateFin.addEventListener('change', function() {
        if (this.value < dateDebut.value) {
            alert('La date de fin ne peut pas être antérieure à la date de début.');
            this.value = '';
        }
    });
});
</script>
{% endblock %}
