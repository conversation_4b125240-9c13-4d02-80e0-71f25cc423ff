from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth import authenticate
from .models import CustomUser, Stagiaire, TacheStage, Mission, RapportStage


class CustomUserCreationForm(UserCreationForm):
    """Formulaire d'inscription personnalisé avec le champ rôle"""
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Adresse email'
        })
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Prénom'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nom'
        })
    )
    
    role = forms.ChoiceField(
        choices=[('ADMIN', 'Administrateur'), ('RH', 'Gestionnaire RH'), ('ENCADRANT', 'Encadrant')],
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': "Nom d'utilisateur"
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Mot de passe'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirmer le mot de passe'
        })

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.role = self.cleaned_data['role']
        if commit:
            user.save()
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """Formulaire de connexion personnalisé"""
    
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': "Nom d'utilisateur",
            'autofocus': True
        })
    )
    
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Mot de passe'
        })
    )


class StagiaireForm(forms.ModelForm):
    """Formulaire pour ajouter/modifier un stagiaire"""

    class Meta:
        model = Stagiaire
        fields = [
            'nom', 'prenom', 'email', 'telephone', 'date_naissance',
            'departement', 'encadrant', 'date_debut', 'date_fin',
            'etablissement', 'niveau_etude', 'specialite', 'statut'
        ]
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du stagiaire'
            }),
            'prenom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Prénom du stagiaire'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+33 1 23 45 67 89'
            }),
            'date_naissance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'departement': forms.Select(attrs={
                'class': 'form-control'
            }),
            'encadrant': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date_debut': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'etablissement': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de l\'établissement'
            }),
            'niveau_etude': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Master 2, Licence 3, etc.'
            }),
            'specialite': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Informatique, Marketing, etc.'
            }),
            'statut': forms.Select(attrs={
                'class': 'form-control'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limiter les encadrants aux utilisateurs ayant le rôle ENCADRANT
        self.fields['encadrant'].queryset = CustomUser.objects.filter(role='ENCADRANT')
        self.fields['encadrant'].empty_label = "Sélectionner un encadrant"

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut')
        date_fin = cleaned_data.get('date_fin')

        if date_debut and date_fin:
            if date_fin <= date_debut:
                raise forms.ValidationError("La date de fin doit être postérieure à la date de début.")

        return cleaned_data

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if username and password:
            self.user_cache = authenticate(
                self.request,
                username=username,
                password=password
            )
            if self.user_cache is None:
                raise forms.ValidationError(
                    "Nom d'utilisateur ou mot de passe incorrect.",
                    code='invalid_login',
                )
            else:
                self.confirm_login_allowed(self.user_cache)

        return self.cleaned_data


class ConventionUploadForm(forms.ModelForm):
    """Formulaire pour l'upload et la validation des conventions de stage"""

    class Meta:
        model = Stagiaire
        fields = ['convention_stage', 'commentaire_convention']
        widgets = {
            'convention_stage': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'commentaire_convention': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Commentaires sur la convention...'
            })
        }


class ConventionValidationForm(forms.ModelForm):
    """Formulaire pour la validation des conventions par les RH"""

    class Meta:
        model = Stagiaire
        fields = ['statut_convention', 'commentaire_convention']
        widgets = {
            'statut_convention': forms.Select(attrs={
                'class': 'form-control'
            }),
            'commentaire_convention': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Commentaires de validation...'
            })
        }


class TacheStageForm(forms.ModelForm):
    """Formulaire pour créer/modifier les tâches de stage"""

    class Meta:
        model = TacheStage
        fields = ['titre', 'description', 'date_debut_prevue', 'date_fin_prevue',
                 'priorite', 'statut']
        widgets = {
            'titre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre de la tâche'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Description détaillée de la tâche...'
            }),
            'date_debut_prevue': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin_prevue': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'priorite': forms.Select(attrs={
                'class': 'form-control'
            }, choices=[(i, f'Priorité {i}') for i in range(1, 6)]),
            'statut': forms.Select(attrs={
                'class': 'form-control'
            })
        }

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut_prevue')
        date_fin = cleaned_data.get('date_fin_prevue')

        if date_debut and date_fin and date_debut >= date_fin:
            raise forms.ValidationError(
                "La date de fin doit être postérieure à la date de début."
            )

        return cleaned_data


class EvaluationStageForm(forms.ModelForm):
    """Formulaire pour l'évaluation du stage par l'encadrant"""

    class Meta:
        model = Stagiaire
        fields = ['statut_taches', 'evaluation_encadrant', 'note_finale']
        widgets = {
            'statut_taches': forms.Select(attrs={
                'class': 'form-control'
            }),
            'evaluation_encadrant': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Évaluation détaillée du stagiaire...'
            }),
            'note_finale': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '20',
                'step': '0.5',
                'placeholder': 'Note sur 20'
            })
        }

    def clean_note_finale(self):
        note = self.cleaned_data.get('note_finale')
        if note is not None and (note < 0 or note > 20):
            raise forms.ValidationError("La note doit être comprise entre 0 et 20.")
        return note


class AttestationGenerationForm(forms.Form):
    """Formulaire pour générer une attestation de fin de stage"""

    template_choice = forms.ChoiceField(
        choices=[
            ('standard', 'Modèle standard'),
            ('detaille', 'Modèle détaillé'),
            ('personnalise', 'Modèle personnalisé')
        ],
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Modèle d'attestation"
    )

    commentaires_supplementaires = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Commentaires supplémentaires à inclure dans l\'attestation...'
        }),
        required=False,
        label="Commentaires supplémentaires"
    )


class MissionForm(forms.ModelForm):
    """Formulaire pour créer/modifier une mission"""

    class Meta:
        model = Mission
        fields = ['titre', 'description', 'objectifs', 'livrables_attendus',
                 'date_debut_prevue', 'date_fin_prevue', 'priorite']
        widgets = {
            'titre': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'objectifs': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'livrables_attendus': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'date_debut_prevue': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'date_fin_prevue': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'priorite': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        self.stagiaire = kwargs.pop('stagiaire', None)
        super().__init__(*args, **kwargs)

        if self.stagiaire:
            # Limiter les dates aux dates du stage
            self.fields['date_debut_prevue'].widget.attrs.update({
                'min': self.stagiaire.date_debut.strftime('%Y-%m-%d'),
                'max': self.stagiaire.date_fin.strftime('%Y-%m-%d')
            })
            self.fields['date_fin_prevue'].widget.attrs.update({
                'min': self.stagiaire.date_debut.strftime('%Y-%m-%d'),
                'max': self.stagiaire.date_fin.strftime('%Y-%m-%d')
            })

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut_prevue')
        date_fin = cleaned_data.get('date_fin_prevue')

        if date_debut and date_fin:
            if date_fin <= date_debut:
                raise forms.ValidationError("La date de fin doit être postérieure à la date de début.")

            if self.stagiaire:
                if date_debut < self.stagiaire.date_debut or date_debut > self.stagiaire.date_fin:
                    raise forms.ValidationError("La date de début doit être dans la période du stage.")
                if date_fin < self.stagiaire.date_debut or date_fin > self.stagiaire.date_fin:
                    raise forms.ValidationError("La date de fin doit être dans la période du stage.")

        return cleaned_data


class SuiviAvancementForm(forms.ModelForm):
    """Formulaire pour le suivi d'avancement d'une mission"""

    class Meta:
        model = Mission
        fields = ['pourcentage_avancement', 'commentaire_avancement', 'statut']
        widgets = {
            'pourcentage_avancement': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 100,
                'step': 5
            }),
            'commentaire_avancement': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'statut': forms.Select(attrs={'class': 'form-select'}),
        }

    def clean_pourcentage_avancement(self):
        pourcentage = self.cleaned_data.get('pourcentage_avancement')
        if pourcentage is not None and (pourcentage < 0 or pourcentage > 100):
            raise forms.ValidationError("Le pourcentage doit être entre 0 et 100.")
        return pourcentage


class RapportStageForm(forms.ModelForm):
    """Formulaire pour soumettre un rapport de stage"""

    class Meta:
        model = RapportStage
        fields = ['titre', 'description', 'fichier_rapport', 'mission']
        widgets = {
            'titre': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'fichier_rapport': forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.doc,.docx'}),
            'mission': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        self.stagiaire = kwargs.pop('stagiaire', None)
        super().__init__(*args, **kwargs)

        if self.stagiaire:
            # Limiter les missions à celles du stagiaire
            self.fields['mission'].queryset = Mission.objects.filter(stagiaire=self.stagiaire)
            self.fields['mission'].empty_label = "Sélectionner une mission (optionnel)"


class ValidationRapportForm(forms.ModelForm):
    """Formulaire pour valider un rapport par l'encadrant"""

    class Meta:
        model = RapportStage
        fields = ['statut', 'commentaires_encadrant', 'note_rapport']
        widgets = {
            'statut': forms.Select(attrs={'class': 'form-select'}),
            'commentaires_encadrant': forms.Textarea(attrs={'class': 'form-control', 'rows': 5}),
            'note_rapport': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 20,
                'step': 0.5
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limiter les choix de statut pour la validation
        self.fields['statut'].choices = [
            ('EN_REVISION', 'En révision'),
            ('VALIDE', 'Validé'),
            ('REJETE', 'Rejeté'),
        ]

    def clean_note_rapport(self):
        note = self.cleaned_data.get('note_rapport')
        if note is not None and (note < 0 or note > 20):
            raise forms.ValidationError("La note doit être entre 0 et 20.")
        return note
