{% extends 'stagiaires/base.html' %}

{% block title %}Gestion des Conventions{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Gestion des Conventions de Stage
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Statistiques -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h5 class="text-primary">{{ stagiaires|length }}</h5>
                                    <small>Total conventions</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h5 class="text-warning">
                                        {% for s in stagiaires %}{% if s.statut_convention == 'EN_ATTENTE' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                                    </h5>
                                    <small>En attente</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h5 class="text-success">
                                        {% for s in stagiaires %}{% if s.statut_convention == 'VALIDEE' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                                    </h5>
                                    <small>Validées</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h5 class="text-danger">
                                        {% for s in stagiaires %}{% if s.statut_convention == 'REJETEE' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                                    </h5>
                                    <small>Rejetées</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filtres -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un stagiaire...">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <select class="form-control" id="statusFilter">
                                <option value="">Tous les statuts</option>
                                <option value="EN_ATTENTE">En attente</option>
                                <option value="VALIDEE">Validée</option>
                                <option value="REJETEE">Rejetée</option>
                                <option value="MODIFIEE">À modifier</option>
                            </select>
                        </div>
                    </div>

                    <!-- Liste des conventions -->
                    {% if stagiaires %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="conventionsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Stagiaire</th>
                                    <th>Département</th>
                                    <th>Encadrant</th>
                                    <th>Période</th>
                                    <th>Statut</th>
                                    <th>Date upload</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stagiaire in stagiaires %}
                                <tr data-status="{{ stagiaire.statut_convention }}" data-name="{{ stagiaire.nom_complet|lower }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                            </div>
                                            <div>
                                                <strong>{{ stagiaire.nom_complet }}</strong><br>
                                                <small class="text-muted">{{ stagiaire.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ stagiaire.get_departement_display }}</span>
                                    </td>
                                    <td>
                                        {% if stagiaire.encadrant %}
                                            {{ stagiaire.encadrant.get_full_name }}
                                        {% else %}
                                            <span class="text-muted">Non assigné</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            {{ stagiaire.date_debut|date:"d/m/Y" }}<br>
                                            {{ stagiaire.date_fin|date:"d/m/Y" }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                            {{ stagiaire.get_statut_convention_display }}
                                        </span>
                                        {% if stagiaire.date_validation_convention %}
                                            <br><small class="text-muted">{{ stagiaire.date_validation_convention|date:"d/m/Y" }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ stagiaire.date_creation|date:"d/m/Y H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if stagiaire.convention_stage %}
                                                <a href="{{ stagiaire.convention_stage.url }}" target="_blank" 
                                                   class="btn btn-sm btn-outline-primary" title="Voir la convention">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'convention_validation' stagiaire.id %}" 
                                                   class="btn btn-sm btn-warning" title="Valider">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            {% endif %}
                                            <a href="{% url 'convention_upload' stagiaire.id %}" 
                                               class="btn btn-sm btn-success" title="Upload/Remplacer">
                                                <i class="fas fa-upload"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune convention trouvée</h5>
                        <p class="text-muted">Les conventions uploadées apparaîtront ici.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>

<script>
// Filtrage et recherche
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const table = document.getElementById('conventionsTable');
    
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const name = row.dataset.name;
            const status = row.dataset.status;
            
            const matchesSearch = name.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            
            row.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }
    
    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
});
</script>
{% endblock %}
