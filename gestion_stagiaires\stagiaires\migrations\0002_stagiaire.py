# Generated by Django 5.1.6 on 2025-07-01 20:16

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Stagiaire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, verbose_name='Nom')),
                ('prenom', models.CharField(max_length=100, verbose_name='Prénom')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('telephone', models.CharField(blank=True, max_length=20, verbose_name='Téléphone')),
                ('date_naissance', models.DateField(verbose_name='Date de naissance')),
                ('departement', models.CharField(choices=[('IT', 'Informatique'), ('MARKETING', 'Marketing'), ('RH', 'Ressources Humaines'), ('FINANCE', 'Finance'), ('COMMERCIAL', 'Commercial'), ('PRODUCTION', 'Production')], max_length=20, verbose_name='Département')),
                ('date_debut', models.DateField(verbose_name='Date de début')),
                ('date_fin', models.DateField(verbose_name='Date de fin')),
                ('statut', models.CharField(choices=[('EN_COURS', 'En cours'), ('TERMINE', 'Terminé'), ('SUSPENDU', 'Suspendu'), ('ANNULE', 'Annulé')], default='EN_COURS', max_length=20, verbose_name='Statut')),
                ('etablissement', models.CharField(max_length=200, verbose_name='Établissement')),
                ('niveau_etude', models.CharField(max_length=100, verbose_name="Niveau d'étude")),
                ('specialite', models.CharField(max_length=100, verbose_name='Spécialité')),
                ('date_creation', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date de création')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stagiaires_crees', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('encadrant', models.ForeignKey(blank=True, limit_choices_to={'role': 'ENCADRANT'}, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Encadrant')),
            ],
            options={
                'verbose_name': 'Stagiaire',
                'verbose_name_plural': 'Stagiaires',
                'ordering': ['-date_creation'],
            },
        ),
    ]
